'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

interface ProgressiveImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  showLoadingSpinner?: boolean;
  loadingSpinnerColor?: string;
}

const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  src,
  alt,
  width,
  height,
  fill = false,
  className = '',
  priority = false,
  quality = 75,
  placeholder = 'blur',
  blurDataURL,
  sizes,
  onLoad,
  onError,
  fallbackSrc = '/images/fallback-image.jpg',
  showLoadingSpinner = true,
  loadingSpinnerColor = 'text-coral-500',
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);
  const imgRef = useRef<HTMLImageElement>(null);

  // Generate a simple blur data URL if none provided
  const generateBlurDataURL = (w: number = 10, h: number = 10) => {
    const canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#f3f4f6';
      ctx.fillRect(0, 0, w, h);
    }
    return canvas.toDataURL();
  };

  const defaultBlurDataURL = blurDataURL || generateBlurDataURL();

  useEffect(() => {
    setImageSrc(src);
    setHasError(false);
    setIsLoading(true);
  }, [src]);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    setImageSrc(fallbackSrc);
    onError?.();
  };

  const imageProps = {
    src: imageSrc,
    alt,
    onLoad: handleLoad,
    onError: handleError,
    quality,
    priority,
    sizes,
    className: `transition-opacity duration-500 ${isLoading ? 'opacity-0' : 'opacity-100'} ${className}`,
    ...(fill ? { fill: true } : { width, height }),
    ...(placeholder === 'blur' ? { 
      placeholder: 'blur' as const, 
      blurDataURL: defaultBlurDataURL 
    } : {}),
  };

  return (
    <div className={`relative overflow-hidden ${fill ? 'w-full h-full' : ''}`}>
      <Image {...imageProps} />
      
      {/* Loading Spinner */}
      <AnimatePresence>
        {isLoading && showLoadingSpinner && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-100"
          >
            <div className={`animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-current ${loadingSpinnerColor}`} />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error State */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500">
          <div className="text-center">
            <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <p className="text-sm">Image not available</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressiveImage;

// Gallery component with progressive loading
interface ImageGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  columns?: number;
  gap?: string;
  className?: string;
  onImageClick?: (index: number) => void;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  columns = 3,
  gap = 'gap-4',
  className = '',
  onImageClick,
}) => {
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set());

  const handleImageLoad = (index: number) => {
    setLoadedImages(prev => new Set([...prev, index]));
  };

  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  };

  return (
    <div className={`grid ${gridCols[columns as keyof typeof gridCols]} ${gap} ${className}`}>
      {images.map((image, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1, duration: 0.5 }}
          className="group cursor-pointer"
          onClick={() => onImageClick?.(index)}
        >
          <div className="relative aspect-square overflow-hidden rounded-xl bg-gray-100">
            <ProgressiveImage
              src={image.src}
              alt={image.alt}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
              onLoad={() => handleImageLoad(index)}
            />
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
            
            {/* Caption */}
            {image.caption && (
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-white text-sm font-medium">{image.caption}</p>
              </div>
            )}
          </div>
        </motion.div>
      ))}
    </div>
  );
};

// Masonry layout for images
export const MasonryGallery: React.FC<ImageGalleryProps> = ({
  images,
  columns = 3,
  gap = 'gap-4',
  className = '',
  onImageClick,
}) => {
  const [columnHeights, setColumnHeights] = useState<number[]>(new Array(columns).fill(0));
  const [imageHeights, setImageHeights] = useState<number[]>([]);

  useEffect(() => {
    // Reset heights when images change
    setColumnHeights(new Array(columns).fill(0));
    setImageHeights([]);
  }, [images, columns]);

  const getShortestColumn = () => {
    return columnHeights.indexOf(Math.min(...columnHeights));
  };

  const handleImageLoad = (index: number, height: number) => {
    setImageHeights(prev => {
      const newHeights = [...prev];
      newHeights[index] = height;
      return newHeights;
    });
  };

  return (
    <div className={`columns-${columns} ${gap} ${className}`} style={{ columnCount: columns }}>
      {images.map((image, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="break-inside-avoid mb-4 group cursor-pointer"
          onClick={() => onImageClick?.(index)}
        >
          <div className="relative overflow-hidden rounded-xl bg-gray-100">
            <ProgressiveImage
              src={image.src}
              alt={image.alt}
              width={400}
              height={300}
              className="w-full h-auto group-hover:scale-105 transition-transform duration-500"
            />
            
            {image.caption && (
              <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-white text-sm">{image.caption}</p>
              </div>
            )}
          </div>
        </motion.div>
      ))}
    </div>
  );
};
